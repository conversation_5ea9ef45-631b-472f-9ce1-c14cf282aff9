'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import gigCategories from '../../../data/gigs/gig-categories.json';
import gigTools from '../../../data/gigs/gig-tools.json';

interface GigRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  freelancer: {
    id: number;
    userId: number;
    name: string;
    title: string;
    avatar: string;
    category: string;
    skills?: string[];
    tools?: string[];
  } | null;
}

export default function GigRequestModal({ isOpen, onClose, freelancer }: GigRequestModalProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    notes: '',
    skills: [] as string[],
    tools: [] as string[],
    budgetMin: '',
    budgetMax: '',
    deliveryTimeWeeks: ''
  });

  // Extract all available skills and tools
  const allSkills = gigCategories.flatMap(cat => [
    cat.label,
    ...cat.subcategories.map(sub => typeof sub === 'string' ? sub : sub.name)
  ]);
  
  const allTools = gigTools.flatMap(cat =>
    cat.tools.map(tool => typeof tool === 'string' ? tool : tool.name)
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!freelancer || !session?.user?.id) return;

    setLoading(true);
    try {
      const response = await fetch('/api/gigs/gig-requests/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          freelancerId: freelancer.id,
          commissionerId: parseInt(session.user.id),
          title: formData.title,
          skills: formData.skills,
          tools: formData.tools,
          notes: formData.notes,
          budget: formData.budgetMin && formData.budgetMax ? {
            min: parseInt(formData.budgetMin),
            max: parseInt(formData.budgetMax),
            currency: 'USD'
          } : undefined,
          deliveryTimeWeeks: formData.deliveryTimeWeeks ? parseInt(formData.deliveryTimeWeeks) : undefined
        }),
      });

      if (response.ok) {
        // Success - close modal and reset form
        onClose();
        setFormData({
          title: '',
          notes: '',
          skills: [],
          tools: [],
          budgetMin: '',
          budgetMax: '',
          deliveryTimeWeeks: ''
        });
        // You could add a success toast here
      } else {
        console.error('Failed to create gig request');
        // You could add an error toast here
      }
    } catch (error) {
      console.error('Error creating gig request:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleSkill = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.includes(skill)
        ? prev.skills.filter(s => s !== skill)
        : [...prev.skills, skill]
    }));
  };

  const toggleTool = (tool: string) => {
    setFormData(prev => ({
      ...prev,
      tools: prev.tools.includes(tool)
        ? prev.tools.filter(t => t !== tool)
        : [...prev.tools, tool]
    }));
  };

  if (!isOpen || !freelancer) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Send Gig Request</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Freelancer Info */}
          <div className="flex items-center gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <Image
              src={freelancer.avatar}
              alt={freelancer.name}
              width={48}
              height={48}
              className="rounded-full"
            />
            <div>
              <h3 className="font-medium text-gray-900">{freelancer.name}</h3>
              <p className="text-sm text-gray-600">{freelancer.title}</p>
              <p className="text-xs text-gray-500">{freelancer.category}</p>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Title *
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#eb1966] focus:border-transparent"
                placeholder="Enter project title"
              />
            </div>

            {/* Project Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Description *
              </label>
              <textarea
                required
                rows={4}
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#eb1966] focus:border-transparent"
                placeholder="Describe your project requirements"
              />
            </div>

            {/* Budget Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Min Budget ($)
                </label>
                <input
                  type="number"
                  value={formData.budgetMin}
                  onChange={(e) => setFormData(prev => ({ ...prev, budgetMin: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#eb1966] focus:border-transparent"
                  placeholder="1000"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Budget ($)
                </label>
                <input
                  type="number"
                  value={formData.budgetMax}
                  onChange={(e) => setFormData(prev => ({ ...prev, budgetMax: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#eb1966] focus:border-transparent"
                  placeholder="5000"
                />
              </div>
            </div>

            {/* Delivery Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Delivery Time (weeks)
              </label>
              <input
                type="number"
                value={formData.deliveryTimeWeeks}
                onChange={(e) => setFormData(prev => ({ ...prev, deliveryTimeWeeks: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#eb1966] focus:border-transparent"
                placeholder="4"
              />
            </div>

            {/* Skills */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Required Skills
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {allSkills.slice(0, 20).map((skill) => (
                  <button
                    key={skill}
                    type="button"
                    onClick={() => toggleSkill(skill)}
                    className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                      formData.skills.includes(skill)
                        ? 'bg-[#eb1966] text-white border-[#eb1966]'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-[#eb1966]'
                    }`}
                  >
                    {skill}
                  </button>
                ))}
              </div>
            </div>

            {/* Tools */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Required Tools
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {allTools.slice(0, 20).map((tool) => (
                  <button
                    key={tool}
                    type="button"
                    onClick={() => toggleTool(tool)}
                    className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                      formData.tools.includes(tool)
                        ? 'bg-[#eb1966] text-white border-[#eb1966]'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-[#eb1966]'
                    }`}
                  >
                    {tool}
                  </button>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex-1 px-4 py-2 bg-[#eb1966] text-white rounded-lg hover:bg-[#d1175a] transition-colors disabled:opacity-50"
              >
                {loading ? 'Sending...' : 'Send Request'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
